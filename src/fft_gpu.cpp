#include "fft_gpu.hpp"
#include <stdexcept>
// #include <immintrin.h>
#include <cuda_runtime.h>
#include <cstring>
#include <unordered_map>

FFTGPUOptimizer::FFTGPUOptimizer(int rows, int cols)
    : ROWS(rows), COLS(cols) {
    int rank = 1;
    int n[1] = { ROWS };
    int istride = COLS;
    int ostride = COLS;
    int idist = 1;
    int odist = 1;
    int inembed[1] = { ROWS };
    int onembed[1] = { ROWS };
    int batch = COLS;

    if (cufftPlanMany(&fft_col_plan_, rank, n,
                      inembed, istride, idist,
                      onembed, ostride, odist,
                      CUFFT_C2C, batch) != CUFFT_SUCCESS) {
        throw std::runtime_error("Failed to create cuFFT PlanMany for column FFT");
    }
}

FFTGPUOptimizer::~FFTGPUOptimizer() {
    cufftDestroy(fft_col_plan_);
}

void FFTGPUOptimizer::performColumnwiseFFT_GPU(float* d_complexData) {
    cufftComplex* d_data = reinterpret_cast<cufftComplex*>(d_complexData);
    if (cufftExecC2C(fft_col_plan_, d_data, d_data, CUFFT_FORWARD) != CUFFT_SUCCESS) {
        throw std::runtime_error("cuFFT column-wise execution failed");
    }
}

float* FFTGPUOptimizer::allocDeviceBuffer(size_t elements) {
    float* d_ptr = nullptr;
    if (cudaMalloc(&d_ptr, elements * sizeof(float)) != cudaSuccess) {
        throw std::runtime_error("CUDA malloc failed");
    }
    return d_ptr;
}

void FFTGPUOptimizer::freeDeviceBuffer(float* d_ptr) {
    cudaFree(d_ptr);
}

void FFTGPUOptimizer::extractColumnsFromCPU(
    const float* h_complexData,
    const std::vector<std::pair<int, int>>& centers,
    std::vector<std::vector<cufftComplex>>& column_data) {
    
    column_data.resize(centers.size());
    
    // 创建列索引到结果的映射，避免重复提取相同列
    std::unordered_map<int, std::vector<cufftComplex>> extracted_columns;
    
    for (size_t i = 0; i < centers.size(); ++i) {
        int col = centers[i].first;
        
        // 检查是否已经提取过该列
        if (extracted_columns.find(col) == extracted_columns.end()) {
            std::vector<cufftComplex> col_data(ROWS);
            
            // 提取该列的所有行数据
            for (int row = 0; row < ROWS; ++row) {
                // 计算该行该列在原始数据中的位置
                size_t src_idx = row * COLS * 2 + col * 2;  // 每个复数占2个float
                
                // 读取实部和虚部
                col_data[row].x = h_complexData[src_idx];
                col_data[row].y = h_complexData[src_idx + 1];
            }
            
            extracted_columns[col] = col_data;
        }
        
        // 复制提取的列数据
        column_data[i] = extracted_columns[col];
    }
}

// 单列FFT
std::vector<std::complex<float>> FFTGPUOptimizer::performColumnwiseFFTForCenters(
    const std::vector<std::pair<int, int>>& centers,
    const float* h_complexData) {
    
        // 结果向量，每个center对应一个复数
    std::vector<std::complex<float>> results(centers.size());
    
    // 在CPU上提取所有需要的列数据
    std::vector<std::vector<cufftComplex>> column_data;
    extractColumnsFromCPU(h_complexData, centers, column_data);
    
    // 分配GPU内存用于存储所有列数据
    cufftComplex* d_columns = nullptr;
    if (cudaMalloc(&d_columns, centers.size() * ROWS * sizeof(cufftComplex)) != cudaSuccess) {
        throw std::runtime_error("CUDA malloc failed for columns data");
    }
    
    // 分配GPU内存用于存储FFT结果
    cufftComplex* d_fft_results = nullptr;
    if (cudaMalloc(&d_fft_results, centers.size() * ROWS * sizeof(cufftComplex)) != cudaSuccess) {
        cudaFree(d_columns);
        throw std::runtime_error("CUDA malloc failed for FFT results");
    }
    
    // 将列数据上传到GPU
    for (size_t i = 0; i < centers.size(); ++i) {
        cudaMemcpy(d_columns + i * ROWS, column_data[i].data(), 
                  ROWS * sizeof(cufftComplex), cudaMemcpyHostToDevice);
    }
    
    // 创建批量一维FFT计划
    cufftHandle fft_plan;
    int rank = 1;
    int n[1] = { ROWS };
    int istride = 1;
    int ostride = 1;
    int idist = ROWS;
    int odist = ROWS;
    
    if (cufftPlanMany(&fft_plan, rank, n,
                      nullptr, istride, idist,
                      nullptr, ostride, odist,
                      CUFFT_C2C, centers.size()) != CUFFT_SUCCESS) {
        cudaFree(d_columns);
        cudaFree(d_fft_results);
        throw std::runtime_error("Failed to create batch 1D cuFFT plan");
    }
    
    // 执行批量FFT
    if (cufftExecC2C(fft_plan, d_columns, d_fft_results, CUFFT_FORWARD) != CUFFT_SUCCESS) {
        cufftDestroy(fft_plan);
        cudaFree(d_columns);
        cudaFree(d_fft_results);
        throw std::runtime_error("Batch 1D cuFFT execution failed");
    }
    
    // 将结果复制回CPU
    std::vector<cufftComplex> h_fft_results(centers.size() * ROWS);
    cudaMemcpy(h_fft_results.data(), d_fft_results, 
               centers.size() * ROWS * sizeof(cufftComplex), cudaMemcpyDeviceToHost);
    
    // 提取每个center对应点的FFT结果
    for (size_t i = 0; i < centers.size(); ++i) {
        int row = centers[i].second;
        
        // 检查行是否在有效范围内
        if (row < 0 || row >= ROWS) {
            results[i] = std::complex<float>(0.0f, 0.0f);
        } else {
            results[i] = std::complex<float>(h_fft_results[i * ROWS + row].x, 
                                           h_fft_results[i * ROWS + row].y);
        }
    }
    
    // 清理资源
    cufftDestroy(fft_plan);
    cudaFree(d_columns);
    cudaFree(d_fft_results);
    
    return results;
}